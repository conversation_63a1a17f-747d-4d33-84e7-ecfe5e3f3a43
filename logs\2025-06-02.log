2025-06-02 16:11:32,218 - INFO - Logger initialized, log directory: F:\software\Cursor软件相关\cursor-auto-free\CursorPro-Windows\logs
2025-06-02 16:11:32,930 - INFO - 可能您的Cursor不是默认安装路径,请创建软连接,命令如下:
2025-06-02 16:11:32,931 - INFO - cmd /c mklink /d "C:\Users\<USER>\AppData\Local\Programs\Cursor" "默认安装路径"
2025-06-02 16:11:32,931 - INFO - 例如:
2025-06-02 16:11:32,931 - INFO - cmd /c mklink /d "C:\Users\<USER>\AppData\Local\Programs\Cursor" "D:\SoftWare\cursor"
2025-06-02 16:12:52,488 - INFO - Logger initialized, log directory: F:\software\Cursor软件相关\cursor-auto-free\CursorPro-Windows\logs
2025-06-02 16:12:53,134 - INFO - 可能您的Cursor不是默认安装路径,请创建软连接,命令如下:
2025-06-02 16:12:53,135 - INFO - cmd /c mklink /d "C:\Users\<USER>\AppData\Local\Programs\Cursor" "默认安装路径"
2025-06-02 16:12:53,135 - INFO - 例如:
2025-06-02 16:12:53,135 - INFO - cmd /c mklink /d "C:\Users\<USER>\AppData\Local\Programs\Cursor" "D:\SoftWare\cursor"
2025-06-02 16:20:34,211 - INFO - Logger initialized, log directory: F:\software\Cursor软件相关\cursor-auto-free\CursorPro-Windows\logs
2025-06-02 16:20:34,889 - INFO - 
=== 初始化程序 ===
2025-06-02 16:20:34,889 - INFO - 开始退出Cursor...
2025-06-02 16:20:35,135 - INFO - 未发现运行中的 Cursor 进程
2025-06-02 16:20:41,041 - INFO - 正在初始化浏览器...
2025-06-02 16:20:41,589 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Converted retries value: 0 -> Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-06-02 16:20:41,589 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Converted retries value: 0 -> Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-06-02 16:20:41,590 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Starting new HTTP connection (1): 127.0.0.1:55284
2025-06-02 16:20:42,094 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] http://127.0.0.1:55284 "GET /json HTTP/1.1" 200 1539
2025-06-02 16:20:42,094 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Converted retries value: 0 -> Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-06-02 16:20:42,095 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Converted retries value: 0 -> Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-06-02 16:20:42,095 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Starting new HTTP connection (1): 127.0.0.1:55284
2025-06-02 16:20:42,096 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] http://127.0.0.1:55284 "GET /json/version HTTP/1.1" 200 432
2025-06-02 16:20:42,098 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Converted retries value: 0 -> Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-06-02 16:20:42,098 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Converted retries value: 0 -> Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-06-02 16:20:42,098 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Starting new HTTP connection (1): 127.0.0.1:55284
2025-06-02 16:20:42,099 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] http://127.0.0.1:55284 "GET /json HTTP/1.1" 200 1539
2025-06-02 16:20:42,099 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Converted retries value: 0 -> Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-06-02 16:20:42,099 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Converted retries value: 0 -> Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-06-02 16:20:42,099 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Starting new HTTP connection (1): 127.0.0.1:55284
2025-06-02 16:20:42,100 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] http://127.0.0.1:55284 "GET /json HTTP/1.1" 200 1539
2025-06-02 16:20:43,758 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Converted retries value: 0 -> Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-06-02 16:20:43,758 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Converted retries value: 0 -> Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-06-02 16:20:43,759 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Starting new HTTP connection (1): 127.0.0.1:49745
2025-06-02 16:20:44,275 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] http://127.0.0.1:49745 "GET /json HTTP/1.1" 200 1539
2025-06-02 16:20:44,275 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Converted retries value: 0 -> Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-06-02 16:20:44,275 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Converted retries value: 0 -> Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-06-02 16:20:44,276 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Starting new HTTP connection (1): 127.0.0.1:49745
2025-06-02 16:20:44,276 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] http://127.0.0.1:49745 "GET /json/version HTTP/1.1" 200 424
2025-06-02 16:20:44,279 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Converted retries value: 0 -> Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-06-02 16:20:44,279 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Converted retries value: 0 -> Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-06-02 16:20:44,280 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Starting new HTTP connection (1): 127.0.0.1:49745
2025-06-02 16:20:44,281 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] http://127.0.0.1:49745 "GET /json HTTP/1.1" 200 1539
2025-06-02 16:20:44,281 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Converted retries value: 0 -> Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-06-02 16:20:44,281 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Converted retries value: 0 -> Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-06-02 16:20:44,282 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] Starting new HTTP connection (1): 127.0.0.1:49745
2025-06-02 16:20:44,282 - DEBUG - [开源项目：https://github.com/chengazhen/cursor-auto-free] http://127.0.0.1:49745 "GET /json HTTP/1.1" 200 1539
2025-06-02 16:20:44,374 - INFO - 请前往开源项目查看更多信息：https://github.com/chengazhen/cursor-auto-free
2025-06-02 16:20:44,374 - INFO - 
=== 配置信息 ===
2025-06-02 16:20:44,375 - INFO - 正在生成随机账号信息...
2025-06-02 16:20:44,375 - ERROR - 程序执行出现错误: 文件 F:\software\Cursor软件相关\cursor-auto-free\CursorPro-Windows\.env 不存在
2025-06-02 16:20:44,376 - ERROR - Traceback (most recent call last):
  File "cursor_pro_keep_alive.py", line 461, in <module>
  File "cursor_pro_keep_alive.py", line 328, in __init__
  File "config.py", line 21, in __init__
FileNotFoundError: 文件 F:\software\Cursor软件相关\cursor-auto-free\CursorPro-Windows\.env 不存在

